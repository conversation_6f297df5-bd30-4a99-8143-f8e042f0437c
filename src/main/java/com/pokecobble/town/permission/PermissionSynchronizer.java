package com.pokecobble.town.permission;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;
import net.minecraft.util.math.ChunkPos;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Handles real-time permission updates and synchronization between server and client.
 */
public class PermissionSynchronizer {
    
    // Network identifiers
    public static final Identifier PERMISSION_UPDATE = new Identifier("pokecobbleclaim", "permission_update");
    public static final Identifier CHUNK_PERMISSION_UPDATE = new Identifier("pokecobbleclaim", "chunk_permission_update");
    public static final Identifier PLAYER_PERMISSION_UPDATE = new Identifier("pokecobbleclaim", "player_permission_update");
    public static final Identifier PERMISSION_SYNC_REQUEST = new Identifier("pokecobbleclaim", "permission_sync_request");
    
    // Server-side permission tracking
    private static final Map<UUID, Integer> playerPermissionVersions = new ConcurrentHashMap<>();
    private static final Map<ChunkPos, Integer> chunkPermissionVersions = new ConcurrentHashMap<>();
    private static final Map<UUID, Set<UUID>> townPlayerSubscriptions = new ConcurrentHashMap<>();
    
    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        // Register permission sync request handler
        ServerPlayNetworking.registerGlobalReceiver(PERMISSION_SYNC_REQUEST, PermissionSynchronizer::handlePermissionSyncRequest);
    }
    
    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Register permission update handler
        ClientPlayNetworking.registerGlobalReceiver(PERMISSION_UPDATE, PermissionSynchronizer::handlePermissionUpdate);
        
        // Register chunk permission update handler
        ClientPlayNetworking.registerGlobalReceiver(CHUNK_PERMISSION_UPDATE, PermissionSynchronizer::handleChunkPermissionUpdate);
        
        // Register player permission update handler
        ClientPlayNetworking.registerGlobalReceiver(PLAYER_PERMISSION_UPDATE, PermissionSynchronizer::handlePlayerPermissionUpdate);
    }
    
    /**
     * Synchronizes permission changes for a player to all relevant clients.
     */
    public static void syncPlayerPermissionChange(MinecraftServer server, UUID playerId, String permissionCategory, String permissionName, boolean value) {
        try {
            // Get the player's town
            Town town = TownManager.getInstance().getPlayerTown(playerId);
            if (town == null) {
                return;
            }
            
            // Update version
            playerPermissionVersions.put(playerId, playerPermissionVersions.getOrDefault(playerId, 0) + 1);
            int version = playerPermissionVersions.get(playerId);
            
            // Create permission update packet
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(playerId);
            buf.writeString(permissionCategory, NetworkConstants.MAX_STRING_LENGTH);
            buf.writeString(permissionName, NetworkConstants.MAX_STRING_LENGTH);
            buf.writeBoolean(value);
            buf.writeInt(version);
            buf.writeLong(System.currentTimeMillis());
            
            // Send to all players in the town
            for (UUID townPlayerId : town.getPlayerIds()) {
                ServerPlayerEntity player = server.getPlayerManager().getPlayer(townPlayerId);
                if (player != null) {
                    NetworkManager.sendToPlayer(player, PLAYER_PERMISSION_UPDATE, buf, true);
                }
            }
            
            // Also send to any subscribed players (e.g., admins viewing permissions)
            Set<UUID> subscribers = townPlayerSubscriptions.get(playerId);
            if (subscribers != null) {
                for (UUID subscriberId : subscribers) {
                    ServerPlayerEntity subscriber = server.getPlayerManager().getPlayer(subscriberId);
                    if (subscriber != null && !town.getPlayerIds().contains(subscriberId)) {
                        NetworkManager.sendToPlayer(subscriber, PLAYER_PERMISSION_UPDATE, buf, true);
                    }
                }
            }
            
            Pokecobbleclaim.LOGGER.debug("Synchronized permission change for player " + playerId + ": " + permissionCategory + "." + permissionName + " = " + value);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error synchronizing player permission change: " + e.getMessage());
        }
    }
    
    /**
     * Synchronizes chunk permission changes to all relevant clients.
     */
    public static void syncChunkPermissionChange(MinecraftServer server, ChunkPos chunkPos, ClaimTag newTag) {
        try {
            // Get the town that owns this chunk
            Town town = com.pokecobble.town.claim.ChunkPermissionManager.getInstance().getTownForChunk(chunkPos);
            if (town == null) {
                return;
            }
            
            // Update version
            chunkPermissionVersions.put(chunkPos, chunkPermissionVersions.getOrDefault(chunkPos, 0) + 1);
            int version = chunkPermissionVersions.get(chunkPos);
            
            // Create chunk permission update packet
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeInt(chunkPos.x);
            buf.writeInt(chunkPos.z);
            buf.writeUuid(town.getId());
            buf.writeString(town.getName(), NetworkConstants.MAX_STRING_LENGTH);
            buf.writeInt(version);
            buf.writeLong(System.currentTimeMillis());
            
            // Write tag data
            if (newTag != null) {
                buf.writeBoolean(true);
                buf.writeString(newTag.getName(), NetworkConstants.MAX_STRING_LENGTH);
                buf.writeString(newTag.getDescription(), NetworkConstants.MAX_STRING_LENGTH);
                buf.writeInt(newTag.getColor());
                
                // Write permissions for each rank
                for (TownPlayerRank rank : TownPlayerRank.values()) {
                    boolean[] permissions = newTag.getPermissionsForRank(rank);
                    for (boolean permission : permissions) {
                        buf.writeBoolean(permission);
                    }
                }
                
                // Write non-member permissions
                boolean[] nonMemberPermissions = newTag.getPermissionsForRank(null);
                for (boolean permission : nonMemberPermissions) {
                    buf.writeBoolean(permission);
                }
            } else {
                buf.writeBoolean(false); // No tag
            }
            
            // Send to all online players (they might need to know about chunk permissions)
            for (ServerPlayerEntity player : server.getPlayerManager().getPlayerList()) {
                NetworkManager.sendToPlayer(player, CHUNK_PERMISSION_UPDATE, buf, true);
            }
            
            Pokecobbleclaim.LOGGER.debug("Synchronized chunk permission change for chunk " + chunkPos + " in town " + town.getName());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error synchronizing chunk permission change: " + e.getMessage());
        }
    }
    
    /**
     * Synchronizes rank changes to all relevant clients.
     */
    public static void syncPlayerRankChange(MinecraftServer server, UUID playerId, TownPlayerRank oldRank, TownPlayerRank newRank) {
        try {
            // Get the player's town
            Town town = TownManager.getInstance().getPlayerTown(playerId);
            if (town == null) {
                return;
            }
            
            // Update version
            playerPermissionVersions.put(playerId, playerPermissionVersions.getOrDefault(playerId, 0) + 1);
            int version = playerPermissionVersions.get(playerId);
            
            // Create rank change packet
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeUuid(playerId);
            buf.writeInt(oldRank != null ? oldRank.ordinal() : -1);
            buf.writeInt(newRank.ordinal());
            buf.writeInt(version);
            buf.writeLong(System.currentTimeMillis());
            
            // Send to all players in the town
            for (UUID townPlayerId : town.getPlayerIds()) {
                ServerPlayerEntity player = server.getPlayerManager().getPlayer(townPlayerId);
                if (player != null) {
                    NetworkManager.sendToPlayer(player, PERMISSION_UPDATE, buf, true);
                }
            }
            
            // Trigger a full permission sync for the affected player
            com.pokecobble.town.network.player.PlayerDataSynchronizer.syncPlayerData(server, playerId);
            
            Pokecobbleclaim.LOGGER.debug("Synchronized rank change for player " + playerId + ": " + oldRank + " -> " + newRank);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error synchronizing rank change: " + e.getMessage());
        }
    }
    
    /**
     * Subscribes a player to permission updates for another player.
     */
    public static void subscribeToPlayerPermissions(UUID subscriberId, UUID targetPlayerId) {
        townPlayerSubscriptions.computeIfAbsent(targetPlayerId, k -> new HashSet<>()).add(subscriberId);
        Pokecobbleclaim.LOGGER.debug("Player " + subscriberId + " subscribed to permission updates for " + targetPlayerId);
    }
    
    /**
     * Unsubscribes a player from permission updates for another player.
     */
    public static void unsubscribeFromPlayerPermissions(UUID subscriberId, UUID targetPlayerId) {
        Set<UUID> subscribers = townPlayerSubscriptions.get(targetPlayerId);
        if (subscribers != null) {
            subscribers.remove(subscriberId);
            if (subscribers.isEmpty()) {
                townPlayerSubscriptions.remove(targetPlayerId);
            }
        }
        Pokecobbleclaim.LOGGER.debug("Player " + subscriberId + " unsubscribed from permission updates for " + targetPlayerId);
    }
    
    /**
     * Clears all subscriptions for a player (called on disconnect).
     */
    public static void clearPlayerSubscriptions(UUID playerId) {
        // Remove as subscriber
        for (Set<UUID> subscribers : townPlayerSubscriptions.values()) {
            subscribers.remove(playerId);
        }
        townPlayerSubscriptions.entrySet().removeIf(entry -> entry.getValue().isEmpty());
        
        // Remove as target
        townPlayerSubscriptions.remove(playerId);
        
        // Clear version tracking
        playerPermissionVersions.remove(playerId);
        
        Pokecobbleclaim.LOGGER.debug("Cleared permission subscriptions for player " + playerId);
    }
    
    /**
     * Handles permission sync request from client.
     */
    private static void handlePermissionSyncRequest(MinecraftServer server, ServerPlayerEntity player,
                                                  net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                                  PacketByteBuf buf,
                                                  net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            UUID playerId = player.getUuid();
            int requestType = buf.readInt(); // 0=own permissions, 1=chunk permissions, 2=all
            
            switch (requestType) {
                case 0: // Own permissions
                    com.pokecobble.town.network.player.PlayerDataSynchronizer.syncPlayerData(server, playerId);
                    break;
                case 1: // Chunk permissions
                    // Sync chunk data which includes permissions
                    com.pokecobble.town.network.chunk.ChunkDataSynchronizer.syncPlayerChunkData(server, playerId);
                    break;
                case 2: // All permissions
                    com.pokecobble.town.network.player.PlayerDataSynchronizer.syncPlayerData(server, playerId);
                    com.pokecobble.town.network.chunk.ChunkDataSynchronizer.syncPlayerChunkData(server, playerId);
                    break;
            }
            
            Pokecobbleclaim.LOGGER.debug("Handled permission sync request from player " + player.getName().getString() + " (type " + requestType + ")");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling permission sync request: " + e.getMessage());
        }
    }
    
    /**
     * Client-side handlers
     */
    
    @Environment(EnvType.CLIENT)
    private static void handlePermissionUpdate(net.minecraft.client.MinecraftClient client,
                                             net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                             PacketByteBuf buf,
                                             net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            UUID playerId = buf.readUuid();
            int oldRank = buf.readInt();
            int newRank = buf.readInt();
            int version = buf.readInt();
            long timestamp = buf.readLong();
            
            // Update client-side permission cache
            com.pokecobble.town.client.ClientPlayerManager.getInstance().updatePlayerRank(playerId, TownPlayerRank.values()[newRank], version);
            
            // Fire permission update event
            com.pokecobble.town.client.ClientSyncManager.getInstance().fireEvent("permission_updated", Map.of(
                "playerId", playerId,
                "oldRank", oldRank >= 0 ? TownPlayerRank.values()[oldRank] : null,
                "newRank", TownPlayerRank.values()[newRank],
                "version", version,
                "timestamp", timestamp
            ));
            
            Pokecobbleclaim.LOGGER.debug("Received permission update for player " + playerId + ": rank " + newRank);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling permission update: " + e.getMessage());
        }
    }
    
    @Environment(EnvType.CLIENT)
    private static void handleChunkPermissionUpdate(net.minecraft.client.MinecraftClient client,
                                                  net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                                  PacketByteBuf buf,
                                                  net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            int chunkX = buf.readInt();
            int chunkZ = buf.readInt();
            UUID townId = buf.readUuid();
            String townName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
            int version = buf.readInt();
            long timestamp = buf.readLong();
            
            ChunkPos chunkPos = new ChunkPos(chunkX, chunkZ);
            
            // Read tag data
            ClaimTag tag = null;
            if (buf.readBoolean()) {
                String tagName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                String tagDescription = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                int tagColor = buf.readInt();
                
                tag = new ClaimTag(tagName, tagDescription, tagColor);
                
                // Read permissions for each rank
                for (TownPlayerRank rank : TownPlayerRank.values()) {
                    for (int i = 0; i < 8; i++) { // 8 permission types
                        boolean permission = buf.readBoolean();
                        tag.getRankPermissions().setPermission(rank, i, permission);
                    }
                }
                
                // Read non-member permissions
                for (int i = 0; i < 8; i++) {
                    boolean permission = buf.readBoolean();
                    tag.getRankPermissions().setPermission(null, i, permission);
                }
            }
            
            // Update client-side chunk permission cache
            com.pokecobble.town.client.ClientChunkManager.getInstance().updateChunkPermissions(chunkPos, tag, version);
            
            // Fire chunk permission update event
            com.pokecobble.town.client.ClientSyncManager.getInstance().fireEvent("chunk_permission_updated", Map.of(
                "chunkPos", chunkPos,
                "townId", townId,
                "townName", townName,
                "tag", tag,
                "version", version,
                "timestamp", timestamp
            ));
            
            Pokecobbleclaim.LOGGER.debug("Received chunk permission update for chunk " + chunkPos + " in town " + townName);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling chunk permission update: " + e.getMessage());
        }
    }
    
    @Environment(EnvType.CLIENT)
    private static void handlePlayerPermissionUpdate(net.minecraft.client.MinecraftClient client,
                                                   net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                                   PacketByteBuf buf,
                                                   net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            UUID playerId = buf.readUuid();
            String permissionCategory = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
            String permissionName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
            boolean value = buf.readBoolean();
            int version = buf.readInt();
            long timestamp = buf.readLong();
            
            // Update client-side permission cache
            com.pokecobble.town.client.ClientPlayerManager.getInstance().updatePlayerPermission(playerId, permissionCategory, permissionName, value, version);
            
            // Fire player permission update event
            com.pokecobble.town.client.ClientSyncManager.getInstance().fireEvent("player_permission_updated", Map.of(
                "playerId", playerId,
                "permissionCategory", permissionCategory,
                "permissionName", permissionName,
                "value", value,
                "version", version,
                "timestamp", timestamp
            ));
            
            Pokecobbleclaim.LOGGER.debug("Received player permission update for " + playerId + ": " + permissionCategory + "." + permissionName + " = " + value);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling player permission update: " + e.getMessage());
        }
    }
    
    /**
     * Requests permission sync from server.
     */
    @Environment(EnvType.CLIENT)
    public static void requestPermissionSync(int requestType) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeInt(requestType);
            
            NetworkManager.sendToServer(PERMISSION_SYNC_REQUEST, buf);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting permission sync: " + e.getMessage());
        }
    }
}
