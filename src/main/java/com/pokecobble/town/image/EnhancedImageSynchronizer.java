package com.pokecobble.town.image;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * Enhanced image synchronization system with efficient data transfer and caching.
 */
public class EnhancedImageSynchronizer {
    
    // Network identifiers
    public static final Identifier IMAGE_VERSION_CHECK = new Identifier("pokecobbleclaim", "image_version_check");
    public static final Identifier IMAGE_DATA_REQUEST = new Identifier("pokecobbleclaim", "image_data_request");
    public static final Identifier IMAGE_DATA_RESPONSE = new Identifier("pokecobbleclaim", "image_data_response");
    public static final Identifier IMAGE_CHUNK_DATA = new Identifier("pokecobbleclaim", "image_chunk_data");
    public static final Identifier IMAGE_TRANSFER_COMPLETE = new Identifier("pokecobbleclaim", "image_transfer_complete");
    
    // Server-side data
    private static final Map<UUID, Map<String, String>> townImageVersions = new ConcurrentHashMap<>();
    private static final Map<String, byte[]> imageDataCache = new ConcurrentHashMap<>();
    private static final Map<UUID, Set<String>> playerPendingTransfers = new ConcurrentHashMap<>();
    
    // Configuration
    private static final int MAX_CHUNK_SIZE = 32768; // 32KB chunks
    private static final int MAX_CACHED_IMAGES = 50;
    private static final long CACHE_TIMEOUT_MS = 600000; // 10 minutes
    
    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        // Register image data request handler
        ServerPlayNetworking.registerGlobalReceiver(IMAGE_DATA_REQUEST, EnhancedImageSynchronizer::handleImageDataRequest);
    }
    
    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Register version check handler
        ClientPlayNetworking.registerGlobalReceiver(IMAGE_VERSION_CHECK, EnhancedImageSynchronizer::handleVersionCheck);

        // Register image data response handler
        ClientPlayNetworking.registerGlobalReceiver(IMAGE_DATA_RESPONSE, EnhancedImageSynchronizer::handleImageDataResponse);

        // Register image chunk data handler
        ClientPlayNetworking.registerGlobalReceiver(IMAGE_CHUNK_DATA, EnhancedImageSynchronizer::handleImageChunkData);

        // Register transfer complete handler
        ClientPlayNetworking.registerGlobalReceiver(IMAGE_TRANSFER_COMPLETE, EnhancedImageSynchronizer::handleTransferComplete);
    }
    
    /**
     * Synchronizes town image versions with a player.
     */
    public static void syncImageVersions(MinecraftServer server, UUID playerId) {
        ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
        if (player == null) {
            return;
        }
        
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            
            // Get all towns the player should know about
            Collection<Town> towns = TownManager.getInstance().getAllTowns();
            
            // Write number of towns
            buf.writeInt(towns.size());
            
            // Write each town's image version
            for (Town town : towns) {
                buf.writeUuid(town.getId());
                buf.writeString(town.getImage() != null ? town.getImage() : "default", NetworkConstants.MAX_STRING_LENGTH);
                
                // Get or generate version for this town's image
                String version = getTownImageVersion(town.getId(), town.getImage());
                buf.writeString(version, NetworkConstants.MAX_STRING_LENGTH);
            }
            
            // Send packet (bypass rate limiting for automatic image version sync)
            NetworkManager.sendToPlayer(player, IMAGE_VERSION_CHECK, buf, true);
            
            Pokecobbleclaim.LOGGER.debug("Sent image version check to player " + player.getName().getString() + " for " + towns.size() + " towns");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error syncing image versions to player " + playerId + ": " + e.getMessage());
        }
    }
    
    /**
     * Handles version check from server.
     */
    @Environment(EnvType.CLIENT)
    private static void handleVersionCheck(net.minecraft.client.MinecraftClient client,
                                         net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                         PacketByteBuf buf,
                                         net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            TownImageCacheManager cacheManager = TownImageCacheManager.getInstance();
            List<ImageRequest> requests = new ArrayList<>();
            
            // Read number of towns
            int townCount = buf.readInt();
            
            // Check each town's image version
            for (int i = 0; i < townCount; i++) {
                UUID townId = buf.readUuid();
                String imageName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                String serverVersion = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                
                // Check if we need to update this image
                if (!cacheManager.isImageCached(townId, imageName, serverVersion)) {
                    requests.add(new ImageRequest(townId, imageName, serverVersion));
                }
            }
            
            // Request missing or outdated images
            if (!requests.isEmpty()) {
                requestImageData(requests);
            }
            
            Pokecobbleclaim.LOGGER.debug("Processed version check for " + townCount + " towns, requesting " + requests.size() + " images");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling version check: " + e.getMessage());
        }
    }
    
    /**
     * Requests image data from server.
     */
    @Environment(EnvType.CLIENT)
    private static void requestImageData(List<ImageRequest> requests) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            
            // Write number of requests
            buf.writeInt(requests.size());
            
            // Write each request
            for (ImageRequest request : requests) {
                buf.writeUuid(request.townId);
                buf.writeString(request.imageName, NetworkConstants.MAX_STRING_LENGTH);
                buf.writeString(request.version, NetworkConstants.MAX_STRING_LENGTH);
            }
            
            // Send request
            NetworkManager.sendToServer(IMAGE_DATA_REQUEST, buf);
            
            Pokecobbleclaim.LOGGER.debug("Requested " + requests.size() + " images from server");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting image data: " + e.getMessage());
        }
    }
    
    /**
     * Handles image data request from client.
     */
    private static void handleImageDataRequest(MinecraftServer server, ServerPlayerEntity player,
                                             net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                             PacketByteBuf buf,
                                             net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            UUID playerId = player.getUuid();
            
            // Read number of requests
            int requestCount = buf.readInt();
            
            // Process each request
            for (int i = 0; i < requestCount; i++) {
                UUID townId = buf.readUuid();
                String imageName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                String version = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                
                // Send image data to player
                sendImageDataToPlayer(server, player, townId, imageName, version);
            }
            
            Pokecobbleclaim.LOGGER.debug("Processed " + requestCount + " image requests from player " + player.getName().getString());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling image data request: " + e.getMessage());
        }
    }
    
    /**
     * Sends image data to a player.
     */
    private static void sendImageDataToPlayer(MinecraftServer server, ServerPlayerEntity player, 
                                            UUID townId, String imageName, String version) {
        try {
            // Get town
            Town town = TownManager.getInstance().getTownById(townId);
            if (town == null) {
                return;
            }
            
            // Load image data
            byte[] imageData = loadImageData(town, imageName);
            if (imageData == null) {
                return;
            }
            
            // Compress image data
            byte[] compressedData = compressData(imageData);
            
            // Create metadata
            TownImageCacheManager.ImageMetadata metadata = new TownImageCacheManager.ImageMetadata(
                version, imageData.length, 
                TownImageCacheManager.ImageMetadata.generateHash(imageData),
                System.currentTimeMillis()
            );
            
            // Send image data response
            PacketByteBuf responseBuf = PacketByteBufs.create();
            responseBuf.writeUuid(townId);
            responseBuf.writeString(imageName, NetworkConstants.MAX_STRING_LENGTH);
            responseBuf.writeString(version, NetworkConstants.MAX_STRING_LENGTH);
            responseBuf.writeInt(compressedData.length);
            responseBuf.writeInt(imageData.length); // Original size
            responseBuf.writeString(metadata.getHash(), NetworkConstants.MAX_STRING_LENGTH);
            
            NetworkManager.sendToPlayer(player, IMAGE_DATA_RESPONSE, responseBuf, true);
            
            // Send image data in chunks
            sendImageDataInChunks(player, townId, imageName, compressedData);
            
            Pokecobbleclaim.LOGGER.debug("Sent image data for town " + townId + ", image: " + imageName + " (" + compressedData.length + " bytes compressed)");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending image data to player: " + e.getMessage());
        }
    }
    
    /**
     * Sends image data in chunks.
     */
    private static void sendImageDataInChunks(ServerPlayerEntity player, UUID townId, String imageName, byte[] data) {
        try {
            int totalChunks = (int) Math.ceil((double) data.length / MAX_CHUNK_SIZE);
            
            for (int chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
                int start = chunkIndex * MAX_CHUNK_SIZE;
                int end = Math.min(start + MAX_CHUNK_SIZE, data.length);
                int chunkSize = end - start;
                
                PacketByteBuf chunkBuf = PacketByteBufs.create();
                chunkBuf.writeUuid(townId);
                chunkBuf.writeString(imageName, NetworkConstants.MAX_STRING_LENGTH);
                chunkBuf.writeInt(chunkIndex);
                chunkBuf.writeInt(totalChunks);
                chunkBuf.writeInt(chunkSize);
                chunkBuf.writeBytes(data, start, chunkSize);
                
                NetworkManager.sendToPlayer(player, IMAGE_CHUNK_DATA, chunkBuf, true);
            }
            
            // Send transfer complete
            PacketByteBuf completeBuf = PacketByteBufs.create();
            completeBuf.writeUuid(townId);
            completeBuf.writeString(imageName, NetworkConstants.MAX_STRING_LENGTH);
            completeBuf.writeInt(totalChunks);
            
            NetworkManager.sendToPlayer(player, IMAGE_TRANSFER_COMPLETE, completeBuf, true);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending image chunks: " + e.getMessage());
        }
    }
    
    /**
     * Loads image data from disk.
     */
    private static byte[] loadImageData(Town town, String imageName) {
        try {
            // Check cache first
            String cacheKey = town.getId().toString() + ":" + imageName;
            byte[] cachedData = imageDataCache.get(cacheKey);
            if (cachedData != null) {
                return cachedData;
            }
            
            // Load from disk
            Path townDir = Paths.get("town_images", town.getName());
            if (Files.exists(townDir)) {
                for (String ext : new String[]{".png", ".jpg", ".jpeg"}) {
                    Path imagePath = townDir.resolve(imageName + ext);
                    if (Files.exists(imagePath)) {
                        byte[] data = Files.readAllBytes(imagePath);
                        
                        // Cache the data
                        if (imageDataCache.size() < MAX_CACHED_IMAGES) {
                            imageDataCache.put(cacheKey, data);
                        }
                        
                        return data;
                    }
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error loading image data: " + e.getMessage());
        }
        
        return null;
    }
    
    /**
     * Compresses data using GZIP.
     */
    private static byte[] compressData(byte[] data) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (GZIPOutputStream gzos = new GZIPOutputStream(baos)) {
            gzos.write(data);
        }
        return baos.toByteArray();
    }
    
    /**
     * Decompresses GZIP data.
     */
    private static byte[] decompressData(byte[] compressedData) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (GZIPInputStream gzis = new GZIPInputStream(new java.io.ByteArrayInputStream(compressedData))) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = gzis.read(buffer)) != -1) {
                baos.write(buffer, 0, len);
            }
        }
        return baos.toByteArray();
    }
    
    /**
     * Gets or generates a version for a town's image.
     */
    private static String getTownImageVersion(UUID townId, String imageName) {
        Map<String, String> townVersions = townImageVersions.computeIfAbsent(townId, k -> new ConcurrentHashMap<>());
        
        return townVersions.computeIfAbsent(imageName, k -> {
            // Generate version based on file modification time or hash
            try {
                Path townDir = Paths.get("town_images", TownManager.getInstance().getTownById(townId).getName());
                if (Files.exists(townDir)) {
                    for (String ext : new String[]{".png", ".jpg", ".jpeg"}) {
                        Path imagePath = townDir.resolve(imageName + ext);
                        if (Files.exists(imagePath)) {
                            long lastModified = Files.getLastModifiedTime(imagePath).toMillis();
                            return String.valueOf(lastModified);
                        }
                    }
                }
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error generating image version: " + e.getMessage());
            }
            
            // Fallback to timestamp
            return String.valueOf(System.currentTimeMillis());
        });
    }
    
    /**
     * Client-side handlers (stubs for now - would be implemented with actual chunk assembly)
     */
    
    @Environment(EnvType.CLIENT)
    private static void handleImageDataResponse(net.minecraft.client.MinecraftClient client,
                                              net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                              PacketByteBuf buf,
                                              net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        // Implementation would prepare for receiving chunks
        Pokecobbleclaim.LOGGER.debug("Received image data response");
    }
    
    @Environment(EnvType.CLIENT)
    private static void handleImageChunkData(net.minecraft.client.MinecraftClient client,
                                           net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                           PacketByteBuf buf,
                                           net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        // Implementation would assemble chunks
        Pokecobbleclaim.LOGGER.debug("Received image chunk data");
    }
    
    @Environment(EnvType.CLIENT)
    private static void handleTransferComplete(net.minecraft.client.MinecraftClient client,
                                             net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                             PacketByteBuf buf,
                                             net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        // Implementation would finalize image and cache it
        Pokecobbleclaim.LOGGER.debug("Image transfer complete");
    }
    
    /**
     * Represents an image request.
     */
    private static class ImageRequest {
        final UUID townId;
        final String imageName;
        final String version;
        
        ImageRequest(UUID townId, String imageName, String version) {
            this.townId = townId;
            this.imageName = imageName;
            this.version = version;
        }
    }
}
