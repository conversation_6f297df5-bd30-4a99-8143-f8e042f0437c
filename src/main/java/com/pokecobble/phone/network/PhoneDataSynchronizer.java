package com.pokecobble.phone.network;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.app.AppPosition;
import com.pokecobble.phone.notification.PhoneNotification;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Handles synchronization of phone data between server and client.
 */
public class PhoneDataSynchronizer {
    
    // Network identifiers
    public static final Identifier PHONE_DATA_SYNC = new Identifier("pokecobbleclaim", "phone_data_sync");
    public static final Identifier PHONE_APP_POSITIONS_SYNC = new Identifier("pokecobbleclaim", "phone_app_positions_sync");
    public static final Identifier PHONE_NOTIFICATION_SYNC = new Identifier("pokecobbleclaim", "phone_notification_sync");
    public static final Identifier PHONE_SETTINGS_SYNC = new Identifier("pokecobbleclaim", "phone_settings_sync");
    public static final Identifier PHONE_DATA_REQUEST = new Identifier("pokecobbleclaim", "phone_data_request");
    
    // Server-side data storage
    private static final Map<UUID, Map<String, AppPosition>> playerAppPositions = new ConcurrentHashMap<>();
    private static final Map<UUID, List<PhoneNotification>> playerNotifications = new ConcurrentHashMap<>();
    private static final Map<UUID, Map<String, Object>> playerPhoneSettings = new ConcurrentHashMap<>();
    private static final Map<UUID, Integer> playerDataVersions = new ConcurrentHashMap<>();
    
    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        // Register phone data request handler
        ServerPlayNetworking.registerGlobalReceiver(PHONE_DATA_REQUEST, PhoneDataSynchronizer::handlePhoneDataRequest);
        
        // Register app position update handler
        ServerPlayNetworking.registerGlobalReceiver(PHONE_APP_POSITIONS_SYNC, PhoneDataSynchronizer::handleAppPositionUpdate);
        
        // Register phone settings update handler
        ServerPlayNetworking.registerGlobalReceiver(PHONE_SETTINGS_SYNC, PhoneDataSynchronizer::handlePhoneSettingsUpdate);
    }
    
    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Register phone data sync handler
        ClientPlayNetworking.registerGlobalReceiver(PHONE_DATA_SYNC, PhoneDataSynchronizer::handlePhoneDataSync);
        
        // Register app positions sync handler
        ClientPlayNetworking.registerGlobalReceiver(PHONE_APP_POSITIONS_SYNC, PhoneDataSynchronizer::handleAppPositionsSync);
        
        // Register notification sync handler
        ClientPlayNetworking.registerGlobalReceiver(PHONE_NOTIFICATION_SYNC, PhoneDataSynchronizer::handleNotificationSync);
        
        // Register settings sync handler
        ClientPlayNetworking.registerGlobalReceiver(PHONE_SETTINGS_SYNC, PhoneDataSynchronizer::handleSettingsSync);
    }
    
    /**
     * Synchronizes all phone data for a player.
     */
    public static void syncPlayerPhoneData(MinecraftServer server, UUID playerId) {
        ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
        if (player == null) {
            return;
        }
        
        try {
            // Sync app positions
            syncAppPositions(player);
            
            // Sync notifications
            syncNotifications(player);
            
            // Sync settings
            syncSettings(player);
            
            Pokecobbleclaim.LOGGER.debug("Synchronized phone data for player " + player.getName().getString());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error synchronizing phone data for player " + player.getName().getString() + ": " + e.getMessage());
        }
    }
    
    /**
     * Synchronizes app positions for a player.
     */
    private static void syncAppPositions(ServerPlayerEntity player) {
        UUID playerId = player.getUuid();
        Map<String, AppPosition> positions = playerAppPositions.get(playerId);
        
        if (positions == null || positions.isEmpty()) {
            return;
        }
        
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            
            // Write data version
            int version = playerDataVersions.getOrDefault(playerId, 0);
            buf.writeInt(version);
            
            // Write number of app positions
            buf.writeInt(positions.size());
            
            // Write each app position
            for (Map.Entry<String, AppPosition> entry : positions.entrySet()) {
                String appId = entry.getKey();
                AppPosition position = entry.getValue();
                
                buf.writeString(appId, NetworkConstants.MAX_STRING_LENGTH);
                buf.writeInt(position.getGridX());
                buf.writeInt(position.getGridY());
                buf.writeInt(position.getPage());
            }
            
            // Send packet (bypass rate limiting for automatic phone data sync)
            NetworkManager.sendToPlayer(player, PHONE_APP_POSITIONS_SYNC, buf, true);
            
            Pokecobbleclaim.LOGGER.debug("Sent app positions sync to " + player.getName().getString() + " (" + positions.size() + " positions)");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error syncing app positions: " + e.getMessage());
        }
    }
    
    /**
     * Synchronizes notifications for a player.
     */
    private static void syncNotifications(ServerPlayerEntity player) {
        UUID playerId = player.getUuid();
        List<PhoneNotification> notifications = playerNotifications.get(playerId);
        
        if (notifications == null || notifications.isEmpty()) {
            return;
        }
        
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            
            // Write data version
            int version = playerDataVersions.getOrDefault(playerId, 0);
            buf.writeInt(version);
            
            // Write number of notifications
            buf.writeInt(notifications.size());
            
            // Write each notification
            for (PhoneNotification notification : notifications) {
                buf.writeString(notification.getTitle(), NetworkConstants.MAX_STRING_LENGTH);
                buf.writeString(notification.getMessage(), NetworkConstants.MAX_STRING_LENGTH);
                buf.writeInt(notification.getType().ordinal());
                buf.writeInt(notification.getDurationTicks());
                buf.writeInt(notification.getRemainingTicks());
                buf.writeLong(notification.getTimestamp());
                
                // Write optional data
                buf.writeBoolean(notification.getTownName() != null);
                if (notification.getTownName() != null) {
                    buf.writeString(notification.getTownName(), NetworkConstants.MAX_STRING_LENGTH);
                }
                
                buf.writeBoolean(notification.getTownId() != null);
                if (notification.getTownId() != null) {
                    buf.writeString(notification.getTownId(), NetworkConstants.MAX_STRING_LENGTH);
                }
            }
            
            // Send packet (bypass rate limiting for automatic phone notification sync)
            NetworkManager.sendToPlayer(player, PHONE_NOTIFICATION_SYNC, buf, true);
            
            Pokecobbleclaim.LOGGER.debug("Sent notifications sync to " + player.getName().getString() + " (" + notifications.size() + " notifications)");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error syncing notifications: " + e.getMessage());
        }
    }
    
    /**
     * Synchronizes settings for a player.
     */
    private static void syncSettings(ServerPlayerEntity player) {
        UUID playerId = player.getUuid();
        Map<String, Object> settings = playerPhoneSettings.get(playerId);
        
        if (settings == null || settings.isEmpty()) {
            return;
        }
        
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            
            // Write data version
            int version = playerDataVersions.getOrDefault(playerId, 0);
            buf.writeInt(version);
            
            // Write number of settings
            buf.writeInt(settings.size());
            
            // Write each setting
            for (Map.Entry<String, Object> entry : settings.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                
                buf.writeString(key, NetworkConstants.MAX_STRING_LENGTH);
                
                // Write value type and value
                if (value instanceof Boolean) {
                    buf.writeInt(0); // Boolean type
                    buf.writeBoolean((Boolean) value);
                } else if (value instanceof Integer) {
                    buf.writeInt(1); // Integer type
                    buf.writeInt((Integer) value);
                } else if (value instanceof String) {
                    buf.writeInt(2); // String type
                    buf.writeString((String) value, NetworkConstants.MAX_STRING_LENGTH);
                } else if (value instanceof Float) {
                    buf.writeInt(3); // Float type
                    buf.writeFloat((Float) value);
                } else {
                    // Default to string representation
                    buf.writeInt(2);
                    buf.writeString(value.toString(), NetworkConstants.MAX_STRING_LENGTH);
                }
            }
            
            // Send packet (bypass rate limiting for automatic phone settings sync)
            NetworkManager.sendToPlayer(player, PHONE_SETTINGS_SYNC, buf, true);
            
            Pokecobbleclaim.LOGGER.debug("Sent phone settings sync to " + player.getName().getString() + " (" + settings.size() + " settings)");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error syncing phone settings: " + e.getMessage());
        }
    }
    
    /**
     * Handles phone data request from client.
     */
    private static void handlePhoneDataRequest(MinecraftServer server, ServerPlayerEntity player, 
                                             net.minecraft.server.network.ServerPlayNetworkHandler handler, 
                                             PacketByteBuf buf, 
                                             net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            // Read request type
            int requestType = buf.readInt(); // 0=all, 1=positions, 2=notifications, 3=settings
            
            UUID playerId = player.getUuid();
            
            switch (requestType) {
                case 0: // All data
                    syncPlayerPhoneData(server, playerId);
                    break;
                case 1: // App positions only
                    syncAppPositions(player);
                    break;
                case 2: // Notifications only
                    syncNotifications(player);
                    break;
                case 3: // Settings only
                    syncSettings(player);
                    break;
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling phone data request: " + e.getMessage());
        }
    }
    
    /**
     * Handles app position update from client.
     */
    private static void handleAppPositionUpdate(MinecraftServer server, ServerPlayerEntity player,
                                              net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                              PacketByteBuf buf,
                                              net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            UUID playerId = player.getUuid();
            
            // Read app ID and position
            String appId = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
            int gridX = buf.readInt();
            int gridY = buf.readInt();
            int page = buf.readInt();
            
            // Create app position
            AppPosition position = new AppPosition(appId, gridX, gridY, page);
            
            // Store position
            playerAppPositions.computeIfAbsent(playerId, k -> new ConcurrentHashMap<>()).put(appId, position);
            
            // Increment version
            playerDataVersions.put(playerId, playerDataVersions.getOrDefault(playerId, 0) + 1);
            
            Pokecobbleclaim.LOGGER.debug("Updated app position for " + player.getName().getString() + ": " + appId + " -> (" + gridX + ", " + gridY + ", page " + page + ")");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling app position update: " + e.getMessage());
        }
    }
    
    /**
     * Handles phone settings update from client.
     */
    private static void handlePhoneSettingsUpdate(MinecraftServer server, ServerPlayerEntity player,
                                                net.minecraft.server.network.ServerPlayNetworkHandler handler,
                                                PacketByteBuf buf,
                                                net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            UUID playerId = player.getUuid();
            
            // Read setting key and value
            String key = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
            int valueType = buf.readInt();
            
            Object value;
            switch (valueType) {
                case 0: // Boolean
                    value = buf.readBoolean();
                    break;
                case 1: // Integer
                    value = buf.readInt();
                    break;
                case 2: // String
                    value = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                    break;
                case 3: // Float
                    value = buf.readFloat();
                    break;
                default:
                    value = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                    break;
            }
            
            // Store setting
            playerPhoneSettings.computeIfAbsent(playerId, k -> new ConcurrentHashMap<>()).put(key, value);
            
            // Increment version
            playerDataVersions.put(playerId, playerDataVersions.getOrDefault(playerId, 0) + 1);
            
            Pokecobbleclaim.LOGGER.debug("Updated phone setting for " + player.getName().getString() + ": " + key + " = " + value);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling phone settings update: " + e.getMessage());
        }
    }

    /**
     * Client-side handlers
     */

    /**
     * Handles phone data sync from server.
     */
    @Environment(EnvType.CLIENT)
    private static void handlePhoneDataSync(net.minecraft.client.MinecraftClient client,
                                          net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                          PacketByteBuf buf,
                                          net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            // This would trigger a full phone data refresh
            // Implementation depends on phone system architecture
            Pokecobbleclaim.LOGGER.debug("Received phone data sync from server");

            // Fire sync event
            com.pokecobble.town.client.ClientSyncManager.getInstance().fireEvent("phone_data_updated", null);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling phone data sync: " + e.getMessage());
        }
    }

    /**
     * Handles app positions sync from server.
     */
    @Environment(EnvType.CLIENT)
    private static void handleAppPositionsSync(net.minecraft.client.MinecraftClient client,
                                             net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                             PacketByteBuf buf,
                                             net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            // Read data version
            int version = buf.readInt();

            // Read number of positions
            int positionCount = buf.readInt();

            Map<String, AppPosition> positions = new HashMap<>();

            // Read each position
            for (int i = 0; i < positionCount; i++) {
                String appId = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                int gridX = buf.readInt();
                int gridY = buf.readInt();
                int page = buf.readInt();

                AppPosition position = new AppPosition(appId, gridX, gridY, page);
                positions.put(appId, position);
            }

            // Update client-side app position manager
            if (client.player != null) {
                com.pokecobble.phone.app.AppPositionManager.getInstance().updatePositionsFromServer(positions, version);
            }

            // Fire sync event
            com.pokecobble.town.client.ClientSyncManager.getInstance().fireEvent("phone_positions_updated", positions);

            Pokecobbleclaim.LOGGER.debug("Received app positions sync: " + positionCount + " positions (version " + version + ")");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling app positions sync: " + e.getMessage());
        }
    }

    /**
     * Handles notification sync from server.
     */
    @Environment(EnvType.CLIENT)
    private static void handleNotificationSync(net.minecraft.client.MinecraftClient client,
                                             net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                             PacketByteBuf buf,
                                             net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            // Read data version
            int version = buf.readInt();

            // Read number of notifications
            int notificationCount = buf.readInt();

            List<PhoneNotification> notifications = new ArrayList<>();

            // Read each notification
            for (int i = 0; i < notificationCount; i++) {
                String title = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                String message = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                PhoneNotification.Type type = PhoneNotification.Type.values()[buf.readInt()];
                int durationTicks = buf.readInt();
                int remainingTicks = buf.readInt();
                long timestamp = buf.readLong();

                PhoneNotification notification = new PhoneNotification(title, message, type, durationTicks);
                notification.setRemainingTicks(remainingTicks);
                notification.setTimestamp(timestamp);

                // Read optional data
                if (buf.readBoolean()) {
                    notification.setTownName(buf.readString(NetworkConstants.MAX_STRING_LENGTH));
                }

                if (buf.readBoolean()) {
                    notification.setTownId(buf.readString(NetworkConstants.MAX_STRING_LENGTH));
                }

                notifications.add(notification);
            }

            // Update client-side notification manager
            com.pokecobble.phone.notification.PhoneNotificationManager.getInstance().updateNotificationsFromServer(notifications, version);

            // Fire sync event
            com.pokecobble.town.client.ClientSyncManager.getInstance().fireEvent("phone_notifications_updated", notifications);

            Pokecobbleclaim.LOGGER.debug("Received notifications sync: " + notificationCount + " notifications (version " + version + ")");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling notification sync: " + e.getMessage());
        }
    }

    /**
     * Handles settings sync from server.
     */
    @Environment(EnvType.CLIENT)
    private static void handleSettingsSync(net.minecraft.client.MinecraftClient client,
                                         net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                         PacketByteBuf buf,
                                         net.fabricmc.fabric.api.networking.v1.PacketSender sender) {
        try {
            // Read data version
            int version = buf.readInt();

            // Read number of settings
            int settingCount = buf.readInt();

            Map<String, Object> settings = new HashMap<>();

            // Read each setting
            for (int i = 0; i < settingCount; i++) {
                String key = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                int valueType = buf.readInt();

                Object value;
                switch (valueType) {
                    case 0: // Boolean
                        value = buf.readBoolean();
                        break;
                    case 1: // Integer
                        value = buf.readInt();
                        break;
                    case 2: // String
                        value = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                        break;
                    case 3: // Float
                        value = buf.readFloat();
                        break;
                    default:
                        value = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                        break;
                }

                settings.put(key, value);
            }

            // Update client-side settings
            // This would update the phone settings manager

            // Fire sync event
            com.pokecobble.town.client.ClientSyncManager.getInstance().fireEvent("phone_settings_updated", settings);

            Pokecobbleclaim.LOGGER.debug("Received phone settings sync: " + settingCount + " settings (version " + version + ")");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling settings sync: " + e.getMessage());
        }
    }

    /**
     * Utility methods
     */

    /**
     * Adds a notification for a player.
     */
    public static void addNotificationForPlayer(UUID playerId, PhoneNotification notification) {
        playerNotifications.computeIfAbsent(playerId, k -> new ArrayList<>()).add(notification);
        playerDataVersions.put(playerId, playerDataVersions.getOrDefault(playerId, 0) + 1);
    }

    /**
     * Removes expired notifications for a player.
     */
    public static void cleanupExpiredNotifications(UUID playerId) {
        List<PhoneNotification> notifications = playerNotifications.get(playerId);
        if (notifications != null) {
            notifications.removeIf(notification -> notification.getRemainingTicks() <= 0);
        }
    }

    /**
     * Clears all data for a player (called on disconnect).
     */
    public static void clearPlayerData(UUID playerId) {
        playerAppPositions.remove(playerId);
        playerNotifications.remove(playerId);
        playerPhoneSettings.remove(playerId);
        playerDataVersions.remove(playerId);
    }

    /**
     * Gets the data version for a player.
     */
    public static int getPlayerDataVersion(UUID playerId) {
        return playerDataVersions.getOrDefault(playerId, 0);
    }
}
